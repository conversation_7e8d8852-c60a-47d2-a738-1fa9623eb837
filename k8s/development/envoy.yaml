apiVersion: gateway.networking.k8s.io/v1
kind: GatewayClass
metadata:
  name: envoy-gateway-class
spec:
  controllerName: gateway.envoyproxy.io/gatewayclass-controller
---
apiVersion: gateway.networking.k8s.io/v1
kind: Gateway
metadata:
  name: envoy-gateway
  namespace: hotel-management-dev
spec:
  gatewayClassName: envoy-gateway-class # match the GatewayClass name
  listeners:
    - name: http
      protocol: HTTP
      port: 80
      hostname: '*'
---
apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: api-gateway
  namespace: hotel-management-dev
spec:
  parentRefs:
    - name: envoy-gateway # match the Gateway name
  rules:
    - backendRefs:
        - name: user-service
          port: 8001
      matches:
        - path:
            type: PathPrefix
            value: /users