### USER DB
# Service finds pods -> StatefulSet manages pods -> Pods are labeled consistently
apiVersion: v1
kind: Service
metadata:
  name: user-db
  namespace: hotel-management-dev
spec:
  selector:
    app: user-db # routes traffic to pods with this label
  ports:
    - name: user-db
      protocol: TCP
      port: 5432
      targetPort: 5432
  type: ClusterIP
---
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: user-db
  namespace: hotel-management-dev
spec:
  serviceName: user-db # must match the name of the service above - links StatefulSet to the Service for DNS
  replicas: 1
  selector:
    matchLabels:
      app: user-db # must match the label below - manages pods with this label
  template:
    metadata:
      labels:
        app: user-db # labels applied to each pod created by the StatefulSet
    spec:
      containers:
        - name: user-db
          image: postgres:17-alpine
          ports:
            - containerPort: 5432
          resources:
            limits:
              memory: "512Mi"
              cpu: "500m"
            requests:
              memory: "256Mi"
              cpu: "250m"
          env:
            - name: POSTGRES_USER
              valueFrom:
                secretKeyRef:
                  name: user-db
                  key: USERNAME
            - name: POSTGRES_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: user-db
                  key: PASSWORD
            - name: POSTGRES_DB
              valueFrom:
                secretKeyRef:
                  name: user-db
                  key: DB_NAME
            - name: PGDATA
              value: /var/lib/postgresql/data/pgdata
          volumeMounts:
            - name: user-db-data
              mountPath: /var/lib/postgresql/data
              subPath: pgdata
          livenessProbe:
            exec:
              command:
                - sh
                - -c
                - pg_isready -q -d $POSTGRES_DB -U $POSTGRES_USER
              initialDelaySeconds: 30
              timeoutSeconds: 5
              periodSeconds: 10
              failureThreshold: 6
          readinessProbe:
            exec:
              command:
                - sh
                - -c
                - pg_isready -q -d $POSTGRES_DB -U $POSTGRES_USER
            initialDelaySeconds: 10
            timeoutSeconds: 5
            periodSeconds: 10
            failureThreshold: 3
  volumeClaimTemplates:
    - metadata:
        name: user-db-data
      spec:
        accessModes:
          - ReadWriteOnce
        resources:
          requests:
            storage: 10Gi
