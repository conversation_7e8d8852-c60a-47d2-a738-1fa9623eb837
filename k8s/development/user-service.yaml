apiVersion: v1
kind: Service
metadata:
  name: user-service
  namespace: hotel-management-dev
spec:
  selector:
    app: user-service
  ports:
    - name: http
      protocol: TCP
      port: 8001
      targetPort: 8001
  type: ClusterIP
  # type: LoadBalancer
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: user-service
  namespace: hotel-management-dev
spec:
  replicas: 1
  selector:
    matchLabels:
      app: user-service
  template:
    metadata:
      labels:
        app: user-service
    spec:
      containers:
        - name: user-service
          image: user-service
          ports:
            - containerPort: 8001
          resources:
            limits:
              memory: "128Mi"
              cpu: "500m"
            requests:
              memory: "64Mi"
              cpu: "250m"
          livenessProbe:
            httpGet:
              path: /health/live
              port: 8001
            initialDelaySeconds: 10
            periodSeconds: 10
          readinessProbe:
            httpGet:
              path: /health/ready
              port: 8001
              initialDelaySeconds: 5
              periodSeconds: 5
          env:
            - name: HTTP_PORT
              valueFrom:
                configMapKeyRef:
                  name: app-config
                  key: USER_SERVICE_PORT
            - name: DB_URI
              valueFrom:
                secretKeyRef:
                  name: user-db
                  key: URI
            - name: DB_MAX_CONN
              valueFrom:
                secretKeyRef:
                  name: user-db
                  key: MAX_CONNECTIONS
            - name: DB_MAX_CONN_LIFE_TIME
              valueFrom:
                secretKeyRef:
                  name: user-db
                  key: MAX_CONNECTION_LIFETIME
            - name: DB_MIN_CONN
              valueFrom:
                secretKeyRef:
                  name: user-db
                  key: MIN_CONNECTIONS
            - name: DB_MAX_IDLE_TIME
              valueFrom:
                secretKeyRef:
                  name: user-db
                  key: MAX_CONNECTION_IDLE_TIME
            - name: DB_HEALTH_CHECK_PERIOD
              valueFrom:
                secretKeyRef:
                  name: user-db
                  key: HEALTH_CHECK_PERIOD
